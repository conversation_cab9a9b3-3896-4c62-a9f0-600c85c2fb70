{"name": "balloonblastsaga", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "^2.1.2", "@shopify/react-native-skia": "^2.0.1", "@types/matter-js": "^0.19.8", "expo": "~53.0.9", "expo-av": "^15.1.4", "expo-haptics": "^14.1.4", "expo-linear-gradient": "~14.1.4", "expo-status-bar": "~2.2.3", "matter-js": "^0.20.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-game-engine": "^1.2.0", "react-native-reanimated": "^3.17.5", "react-native-svg": "^15.12.0", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}