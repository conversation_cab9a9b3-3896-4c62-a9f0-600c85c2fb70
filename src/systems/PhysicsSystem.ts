import { Balloon } from '../entities/BalloonEntity';
import { LevelConfig } from '../types/GameTypes';

export class PhysicsSystem {
  private screenWidth: number;
  private screenHeight: number;
  private windForce: number = 0;
  private windDirection: number = 1; // 1 for right, -1 for left
  private windChangeTime: number = 0;
  private readonly WIND_CHANGE_INTERVAL = 3000; // Change wind every 3 seconds
  private windType: 'calm' | 'light' | 'moderate' | 'strong' | 'gust' = 'calm';
  private gustStartTime: number = 0;
  private readonly GUST_DURATION = 1500; // Gust lasts 1.5 seconds
  private levelConfig?: LevelConfig;

  constructor(screenWidth: number, screenHeight: number) {
    this.screenWidth = screenWidth;
    this.screenHeight = screenHeight;
    this.updateWind();
  }

  private updateWind(): void {
    // Generate random wind type
    const windTypes: ('calm' | 'light' | 'moderate' | 'strong' | 'gust')[] =
      ['calm', 'light', 'light', 'moderate', 'moderate', 'strong', 'gust'];
    this.windType = windTypes[Math.floor(Math.random() * windTypes.length)];

    // Set wind force based on type
    switch (this.windType) {
      case 'calm':
        this.windForce = Math.random() * 5; // 0-5 pixels per second
        break;
      case 'light':
        this.windForce = 5 + Math.random() * 15; // 5-20 pixels per second
        break;
      case 'moderate':
        this.windForce = 20 + Math.random() * 25; // 20-45 pixels per second
        break;
      case 'strong':
        this.windForce = 45 + Math.random() * 35; // 45-80 pixels per second
        break;
      case 'gust':
        this.windForce = 60 + Math.random() * 40; // 60-100 pixels per second
        this.gustStartTime = Date.now();
        break;
    }

    // Generate random wind direction
    this.windDirection = Math.random() > 0.5 ? 1 : -1;
    this.windChangeTime = Date.now();

    console.log(`[PhysicsSystem] Wind updated: ${this.windType} (${this.windForce.toFixed(1)} px/s) ${this.windDirection > 0 ? 'right' : 'left'}`);
  }

  public updateBalloons(balloons: Balloon[], deltaTime: number): void {
    const currentTime = Date.now();

    // Update wind periodically
    if (currentTime - this.windChangeTime > this.WIND_CHANGE_INTERVAL) {
      this.updateWind();
    }

    // Handle gust duration
    let currentWindForce = this.windForce;
    if (this.windType === 'gust' && currentTime - this.gustStartTime > this.GUST_DURATION) {
      // Gust is over, reduce to moderate wind
      currentWindForce = 20 + Math.random() * 25; // Moderate wind
    }

    // Update balloon positions and apply forces
    balloons.forEach(balloon => {
      if (!balloon.isPopped) {
        // Apply wind force (scaled by mass - lighter balloons affected more by wind)
        // Increased wind effect multiplier from 0.1 to 0.3 for more noticeable impact
        const massEffect = 1 / balloon.mass; // Lighter balloons (lower mass) affected more
        const windEffect = currentWindForce * this.windDirection * deltaTime * 0.3 * massEffect;
        balloon.velocity.x += windEffect;

        // Add turbulence for stronger winds
        if (this.windType === 'strong' || this.windType === 'gust') {
          const turbulence = (Math.random() - 0.5) * 20 * deltaTime * massEffect;
          balloon.velocity.x += turbulence;
          balloon.velocity.y += turbulence * 0.5; // Slight vertical turbulence
        }

        balloon.update(deltaTime);
        this.applyBoundaries(balloon);
      }
    });

    // Handle balloon-to-balloon collisions
    this.handleBalloonCollisions(balloons);
  }

  private applyBoundaries(balloon: Balloon): void {
    const restitution = 0.7; // Bounce dampening factor

    // Left boundary
    if (balloon.position.x - balloon.radius < 0) {
      balloon.position.x = balloon.radius;
      balloon.velocity.x = Math.abs(balloon.velocity.x) * restitution;
    }

    // Right boundary
    if (balloon.position.x + balloon.radius > this.screenWidth) {
      balloon.position.x = this.screenWidth - balloon.radius;
      balloon.velocity.x = -Math.abs(balloon.velocity.x) * restitution;
    }

    // Top boundary - DO NOT BOUNCE, let balloons pass through for penalty system
    // (Balloons escaping through top will be handled in removeOffScreenBalloons)

    // Bottom boundary
    if (balloon.position.y + balloon.radius > this.screenHeight) {
      balloon.position.y = this.screenHeight - balloon.radius;
      balloon.velocity.y = -Math.abs(balloon.velocity.y) * restitution;
    }
  }

  private handleBalloonCollisions(balloons: Balloon[]): void {
    // Check collisions between all pairs of balloons
    for (let i = 0; i < balloons.length; i++) {
      for (let j = i + 1; j < balloons.length; j++) {
        const balloonA = balloons[i];
        const balloonB = balloons[j];

        if (balloonA.isCollidingWith(balloonB)) {
          balloonA.resolveCollision(balloonB);
        }
      }
    }
  }

  public removeOffScreenBalloons(balloons: Balloon[]): { remaining: Balloon[]; escaped: Balloon[] } {
    const remaining: Balloon[] = [];
    const escaped: Balloon[] = [];

    balloons.forEach(balloon => {
      if (balloon.isOffScreen(this.screenHeight)) {
        // Only count as escaped if balloon went off the top (negative y)
        if (balloon.position.y < -balloon.radius) {
          escaped.push(balloon);
        }
        // Balloons that go off other edges are just removed without penalty
      } else {
        remaining.push(balloon);
      }
    });

    return { remaining, escaped };
  }

  public spawnBalloon(): Balloon {
    const x = Math.random() * (this.screenWidth - 60) + 30; // 30px margin
    const y = this.screenHeight + 30; // Start below screen

    // Generate random direction and angle
    const direction = this.getRandomDirection();
    let angle = this.getRandomAngleForDirection(direction);

    // Slightly adjust angle based on wind direction for more realistic spawning
    if (this.windForce > 20) { // Only for moderate+ winds
      const windInfluence = (this.windDirection * this.windForce * 0.001); // Small influence
      angle += windInfluence;
    }

    // Create balloon with custom velocity
    const balloon = new Balloon(x, y, undefined, direction, angle);

    // Apply level-specific speed multiplier if available
    if (this.levelConfig) {
      balloon.applySpeedMultiplier(this.levelConfig.balloonSpeedMultiplier);
    }

    // Apply initial wind effect to newly spawned balloon
    if (this.windForce > 10) {
      const initialWindEffect = this.windForce * this.windDirection * 0.02 / balloon.mass;
      balloon.velocity.x += initialWindEffect;
    }

    // Log balloon spawn details for debugging
    console.log(`[PhysicsSystem] Spawned balloon: direction=${direction}, angle=${(angle * 180 / Math.PI).toFixed(1)}°, velocity=(${balloon.velocity.x.toFixed(1)}, ${balloon.velocity.y.toFixed(1)}), wind=${this.windType}`);

    return balloon;
  }

  private getRandomDirection(): 'top-left' | 'top' | 'top-right' {
    const directions: ('top-left' | 'top' | 'top-right')[] = ['top-left', 'top', 'top-right'];
    return directions[Math.floor(Math.random() * directions.length)];
  }

  private getRandomAngleForDirection(direction: 'top-left' | 'top' | 'top-right'): number {
    // Angles in radians, with some random variation
    const baseAngles = {
      'top-left': Math.PI * 0.75,   // 135 degrees (top-left)
      'top': Math.PI * 0.5,         // 90 degrees (straight up)
      'top-right': Math.PI * 0.25   // 45 degrees (top-right)
    };

    // Add random variation of ±30 degrees (±π/6 radians)
    const variation = (Math.random() - 0.5) * (Math.PI / 3); // ±π/6
    return baseAngles[direction] + variation;
  }

  public updateScreenDimensions(width: number, height: number): void {
    this.screenWidth = width;
    this.screenHeight = height;
  }

  public getWindInfo(): { force: number; direction: number; type: string; isGust: boolean } {
    const currentTime = Date.now();
    const isGust = this.windType === 'gust' && currentTime - this.gustStartTime <= this.GUST_DURATION;

    return {
      force: this.windForce,
      direction: this.windDirection,
      type: this.windType,
      isGust,
    };
  }

  public setLevelConfig(levelConfig: LevelConfig): void {
    this.levelConfig = levelConfig;
  }
}
