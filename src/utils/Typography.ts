import { Platform } from 'react-native';

// Game-friendly font configuration
export const GAME_FONTS = {
  // Primary game font - bold and rounded for headings and important text
  primary: Platform.select({
    ios: 'Avenir-Heavy', // iOS system font with game-like feel
    android: 'sans-serif-black', // Android bold system font
    default: 'Arial-BoldMT',
  }),
  
  // Secondary game font - medium weight for body text
  secondary: Platform.select({
    ios: 'Avenir-Medium',
    android: 'sans-serif-medium',
    default: 'Arial',
  }),
  
  // Display font - extra bold for titles and large text
  display: Platform.select({
    ios: 'Avenir-Black',
    android: 'sans-serif-black',
    default: 'Arial-Black',
  }),
  
  // UI font - clean and readable for interface elements
  ui: Platform.select({
    ios: 'Avenir-Book',
    android: 'sans-serif',
    default: 'Arial',
  }),
};

// Typography styles for consistent game-friendly text
export const TYPOGRAPHY = {
  // Large title text (game title, main headings)
  title: {
    fontFamily: GAME_FONTS.display,
    fontSize: 32,
    fontWeight: '900' as const,
    letterSpacing: 1,
  },
  
  // Section headings (world names, screen titles)
  heading: {
    fontFamily: GAME_FONTS.primary,
    fontSize: 24,
    fontWeight: '800' as const,
    letterSpacing: 0.5,
  },
  
  // Subheadings (level numbers, progress text)
  subheading: {
    fontFamily: GAME_FONTS.primary,
    fontSize: 20,
    fontWeight: '700' as const,
    letterSpacing: 0.3,
  },
  
  // Body text (descriptions, instructions)
  body: {
    fontFamily: GAME_FONTS.secondary,
    fontSize: 16,
    fontWeight: '600' as const,
    letterSpacing: 0.2,
  },
  
  // Small text (labels, captions)
  caption: {
    fontFamily: GAME_FONTS.secondary,
    fontSize: 14,
    fontWeight: '500' as const,
    letterSpacing: 0.1,
  },
  
  // Button text
  button: {
    fontFamily: GAME_FONTS.primary,
    fontSize: 18,
    fontWeight: '700' as const,
    letterSpacing: 0.5,
  },
  
  // Level numbers and game UI
  gameUI: {
    fontFamily: GAME_FONTS.primary,
    fontSize: 16,
    fontWeight: '800' as const,
    letterSpacing: 0.3,
  },
  
  // Score and numbers
  score: {
    fontFamily: GAME_FONTS.display,
    fontSize: 24,
    fontWeight: '900' as const,
    letterSpacing: 0.5,
  },
};

// Helper function to get font style with custom size
export const getGameFont = (
  type: keyof typeof TYPOGRAPHY,
  fontSize?: number,
  color?: string
) => ({
  ...TYPOGRAPHY[type],
  ...(fontSize && { fontSize }),
  ...(color && { color }),
});

// Common game text colors
export const GAME_COLORS = {
  text: {
    primary: '#FFFFFF',
    secondary: '#E0E0E0',
    accent: '#FFD700',
    success: '#4CAF50',
    warning: '#FF9800',
    error: '#F44336',
    disabled: '#9E9E9E',
  },
};
