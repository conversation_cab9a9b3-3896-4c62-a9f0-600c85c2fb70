// Core game types and interfaces

export interface Position {
  x: number;
  y: number;
}

export interface Velocity {
  x: number;
  y: number;
}

export interface BalloonType {
  id: string;
  color: string;
  points: number;
  baseSize: number; // Base size - actual size will be randomized
  special?: boolean;
}

export interface BalloonEntity {
  id: string;
  type: BalloonType;
  position: Position;
  velocity: Velocity;
  isPopped: boolean;
  createdAt: number;
}

export interface GameState {
  score: number;
  level: number;
  balloons: BalloonEntity[];
  isPlaying: boolean;
  timeRemaining: number;
}

export interface TouchEvent {
  x: number;
  y: number;
  timestamp: number;
}

export interface LevelConfig {
  balloonSpeedMultiplier: number;
  balloonSpawnRate: number; // milliseconds between spawns
  world: number;
  level: number;
}

// World map positioning and connections
export interface WorldPosition {
  x: number; // X coordinate as percentage of screen width (0-100)
  y: number; // Y coordinate as percentage of screen height (0-100)
}

export interface WorldConnection {
  from: number; // World number
  to: number; // World number
  pathPoints: Position[]; // Bezier curve control points for smooth paths
}

export interface WorldMapLayout {
  positions: { [worldNumber: number]: WorldPosition };
  connections: WorldConnection[];
  screenWidth: number;
  screenHeight: number;
}

// Level positioning and connections (for level select screen)
export interface LevelPosition {
  x: number; // X coordinate as percentage of screen width (0-100)
  y: number; // Y coordinate as percentage of screen height (0-100)
}

export interface LevelConnection {
  from: number; // Level number
  to: number; // Level number
  pathPoints: Position[]; // Bezier curve control points for smooth paths
}

export interface LevelMapLayout {
  positions: { [levelNumber: number]: LevelPosition };
  connections: LevelConnection[];
  screenWidth: number;
  screenHeight: number;
}

// Balloon types configuration
export const BALLOON_TYPES: { [key: string]: BalloonType } = {
  RED: {
    id: 'red',
    color: '#FF6B6B',
    points: 10,
    baseSize: 60, // Base size - will be randomized
  },
  BLUE: {
    id: 'blue',
    color: '#4ECDC4',
    points: 10,
    baseSize: 60, // Base size - will be randomized
  },
  GREEN: {
    id: 'green',
    color: '#45B7D1',
    points: 10,
    baseSize: 60, // Base size - will be randomized
  },
  YELLOW: {
    id: 'yellow',
    color: '#FFA07A',
    points: 10,
    baseSize: 60, // Base size - will be randomized
  },
  GOLD: {
    id: 'gold',
    color: '#FFD700',
    points: 50,
    baseSize: 60, // Base size - will be randomized
    special: true,
  },
  SILVER: {
    id: 'silver',
    color: '#C0C0C0',
    points: 25,
    baseSize: 60, // Base size - will be randomized
    special: true,
  },
};

export const GAME_CONFIG = {
  BASE_BALLOON_SPAWN_RATE: 2000, // Base spawn rate in milliseconds (level 1)
  BASE_BALLOON_FLOAT_SPEED: 200, // Base speed in pixels per second (level 1)
  GAME_DURATION: 30000, // 60 seconds
  SCREEN_WIDTH: 375, // will be updated with actual screen dimensions
  SCREEN_HEIGHT: 812, // will be updated with actual screen dimensions
  SPEED_INCREASE_PER_LEVEL: 0.2, // Speed multiplier increase per level
  SPAWN_RATE_DECREASE_PER_LEVEL: 150, // Milliseconds decrease per level (faster spawning)
};

// Level configuration calculation functions
export const calculateLevelConfig = (world: number, level: number): LevelConfig => {
  // Speed starts at 0.2 for level 1 and increases by 0.2 each level
  const baseSpeedMultiplier = 0.2;
  const balloonSpeedMultiplier = baseSpeedMultiplier + (level - 1) * GAME_CONFIG.SPEED_INCREASE_PER_LEVEL;

  // Spawn rate starts slower and gets faster each level
  // Level 1: 2000ms, Level 2: 1850ms, Level 3: 1700ms, etc.
  const balloonSpawnRate = Math.max(
    300, // Minimum spawn rate (very fast)
    GAME_CONFIG.BASE_BALLOON_SPAWN_RATE - (level - 1) * GAME_CONFIG.SPAWN_RATE_DECREASE_PER_LEVEL
  );

  return {
    balloonSpeedMultiplier,
    balloonSpawnRate,
    world,
    level,
  };
};

// World map layout generation functions
export const generateWorldMapLayout = (screenWidth: number, screenHeight: number, totalWorlds: number = 5): WorldMapLayout => {
  const positions: { [worldNumber: number]: WorldPosition } = {};
  const connections: WorldConnection[] = [];

  // Define safe margins (percentage of screen)
  const marginX = 15; // 15% margin on each side
  const marginY = 20; // 20% margin on top and bottom

  // Available area for positioning worlds
  const availableWidth = 100 - (marginX * 2);
  const availableHeight = 100 - (marginY * 2);

  // Generate random positions for each world with minimum distance constraints
  const minDistance = 25; // Minimum distance between worlds (percentage)

  for (let world = 1; world <= totalWorlds; world++) {
    let position: WorldPosition;
    let attempts = 0;
    const maxAttempts = 50;

    do {
      position = {
        x: marginX + Math.random() * availableWidth,
        y: marginY + Math.random() * availableHeight,
      };
      attempts++;
    } while (attempts < maxAttempts && !isValidPosition(position, positions, minDistance));

    positions[world] = position;

    // Create connection to previous world (except for world 1)
    if (world > 1) {
      const fromPos = positions[world - 1];
      const toPos = position;

      // Generate smooth path with bezier curve
      const pathPoints = generateSmoothPath(fromPos, toPos, screenWidth, screenHeight);

      connections.push({
        from: world - 1,
        to: world,
        pathPoints,
      });
    }
  }

  return {
    positions,
    connections,
    screenWidth,
    screenHeight,
  };
};

// Helper function to check if a position is valid (not too close to existing worlds)
const isValidPosition = (
  newPosition: WorldPosition,
  existingPositions: { [worldNumber: number]: WorldPosition },
  minDistance: number
): boolean => {
  for (const existingPosition of Object.values(existingPositions)) {
    const distance = Math.sqrt(
      Math.pow(newPosition.x - existingPosition.x, 2) +
      Math.pow(newPosition.y - existingPosition.y, 2)
    );
    if (distance < minDistance) {
      return false;
    }
  }
  return true;
};

// Generate smooth path between two world positions
const generateSmoothPath = (
  from: WorldPosition,
  to: WorldPosition,
  screenWidth: number,
  screenHeight: number
): Position[] => {
  // Convert percentage positions to actual pixel coordinates
  const fromPixel = {
    x: (from.x / 100) * screenWidth,
    y: (from.y / 100) * screenHeight,
  };
  const toPixel = {
    x: (to.x / 100) * screenWidth,
    y: (to.y / 100) * screenHeight,
  };

  // Create bezier curve control points for smooth path
  const midX = (fromPixel.x + toPixel.x) / 2;
  const midY = (fromPixel.y + toPixel.y) / 2;

  // Add some randomness to the curve for more natural paths
  const offsetX = (Math.random() - 0.5) * 100;
  const offsetY = (Math.random() - 0.5) * 100;

  return [
    fromPixel,
    { x: midX + offsetX, y: midY + offsetY },
    toPixel,
  ];
};

// Level map layout generation functions
export const generateLevelMapLayout = (screenWidth: number, screenHeight: number, totalLevels: number = 10): LevelMapLayout => {
  const positions: { [levelNumber: number]: LevelPosition } = {};
  const connections: LevelConnection[] = [];

  // Define compact margins (percentage of screen) to fit all levels
  const marginX = 8; // 8% margin on each side
  const marginTop = 5; // 5% margin on top (compact)
  const marginBottom = 5; // 5% margin on bottom (compact)

  // Available area for positioning levels
  const availableWidth = 100 - (marginX * 2);
  const availableHeight = 100 - marginTop - marginBottom;

  // Calculate compact vertical spacing to fit all levels on screen
  // Use totalLevels instead of (totalLevels - 1) for more compact spacing
  const verticalSpacing = availableHeight / totalLevels;

  for (let level = 1; level <= totalLevels; level++) {
    // More compact vertical positioning - each level takes 1/totalLevels of available height
    const y = marginTop + (level - 0.5) * verticalSpacing; // -0.5 to center within each segment

    // Random horizontal position within safe margins
    const x = marginX + Math.random() * availableWidth;

    positions[level] = { x, y };

    // Create connection to previous level (except for level 1)
    if (level > 1) {
      const fromPos = positions[level - 1];
      const toPos = positions[level];

      // Generate smooth path with bezier curve
      const pathPoints = generateSmoothLevelPath(fromPos, toPos, screenWidth, screenHeight);

      connections.push({
        from: level - 1,
        to: level,
        pathPoints,
      });
    }
  }

  return {
    positions,
    connections,
    screenWidth,
    screenHeight,
  };
};

// Generate smooth path between two level positions
const generateSmoothLevelPath = (
  from: LevelPosition,
  to: LevelPosition,
  screenWidth: number,
  screenHeight: number
): Position[] => {
  // Convert percentage positions to actual pixel coordinates
  const fromPixel = {
    x: (from.x / 100) * screenWidth,
    y: (from.y / 100) * screenHeight,
  };
  const toPixel = {
    x: (to.x / 100) * screenWidth,
    y: (to.y / 100) * screenHeight,
  };

  // Create bezier curve control points for smooth path
  const midX = (fromPixel.x + toPixel.x) / 2;
  const midY = (fromPixel.y + toPixel.y) / 2;

  // Add some curvature to make the path more interesting
  // Curve slightly to the side based on horizontal distance
  const horizontalDistance = toPixel.x - fromPixel.x;
  const curveOffset = horizontalDistance * 0.3; // 30% of horizontal distance

  return [
    fromPixel,
    { x: midX + curveOffset, y: midY },
    toPixel,
  ];
};
