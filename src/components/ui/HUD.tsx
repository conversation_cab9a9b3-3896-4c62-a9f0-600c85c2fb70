import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { GameState } from '../../types/GameTypes';
import { WindIndicator } from './WindIndicator';
import { TYPOGRAPHY } from '../../utils/Typography';

interface HUDProps {
  gameState: GameState;
  combo?: number;
  windInfo?: {
    force: number;
    direction: number;
    type: string;
    isGust: boolean;
  };
}

export const HUD: React.FC<HUDProps> = ({ gameState, combo = 0, windInfo }) => {
  const formatTime = (milliseconds: number): string => {
    const seconds = Math.ceil(milliseconds / 1000);
    return `${seconds}s`;
  };

  return (
    <View style={styles.container}>
      <View style={styles.topRow}>
        <View style={styles.scoreContainer}>
          <Text style={styles.label}>Score</Text>
          <Text style={styles.score}>{gameState.score.toLocaleString()}</Text>
        </View>

        <View style={styles.timeContainer}>
          <Text style={styles.label}>Time</Text>
          <Text style={[
            styles.time,
            { color: gameState.timeRemaining < 10000 ? '#E74C3C' : '#27AE60' }
          ]}>
            {formatTime(gameState.timeRemaining)}
          </Text>
        </View>
      </View>

      <View style={styles.levelContainer}>
        <Text style={styles.level}>Level {gameState.level}</Text>
        {combo > 1 && (
          <Text style={styles.combo}>Combo x{combo}!</Text>
        )}
      </View>

      {/* Wind Indicator */}
      {windInfo && <WindIndicator windInfo={windInfo} />}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    zIndex: 1000,
    paddingHorizontal: 20,
  },
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  scoreContainer: {
    alignItems: 'flex-start',
  },
  timeContainer: {
    alignItems: 'flex-end',
  },
  levelContainer: {
    alignItems: 'center',
    marginTop: 10,
  },
  label: {
    ...TYPOGRAPHY.caption,
    color: '#666',
  },
  score: {
    ...TYPOGRAPHY.score,
    color: '#2E86AB',
  },
  time: {
    ...TYPOGRAPHY.score,
  },
  level: {
    ...TYPOGRAPHY.gameUI,
    fontSize: 18,
    color: '#34495E',
  },
  combo: {
    ...TYPOGRAPHY.gameUI,
    color: '#E74C3C',
    marginTop: 5,
    textAlign: 'center',
  },
});
