import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface WindIndicatorProps {
  windInfo: {
    force: number;
    direction: number;
    type: string;
    isGust: boolean;
  };
}

export const WindIndicator: React.FC<WindIndicatorProps> = ({ windInfo }) => {
  const getWindStrength = (force: number): string => {
    if (force < 5) return 'Calm';
    if (force < 20) return 'Light';
    if (force < 45) return 'Moderate';
    if (force < 80) return 'Strong';
    return 'Very Strong';
  };

  const getWindColor = (type: string, isGust: boolean): string => {
    if (isGust) return '#E74C3C'; // Red for gusts
    switch (type) {
      case 'calm': return '#95A5A6'; // Gray
      case 'light': return '#3498DB'; // Light blue
      case 'moderate': return '#F39C12'; // Orange
      case 'strong': return '#E67E22'; // Dark orange
      case 'gust': return '#E74C3C'; // Red
      default: return '#95A5A6';
    }
  };

  const getArrowRotation = (direction: number): string => {
    // direction: 1 = right, -1 = left
    return direction > 0 ? '0deg' : '180deg';
  };

  const getWindIntensityScale = (force: number): number => {
    // Scale from 0.8 to 1.4 based on wind force
    return 0.8 + (Math.min(force, 100) / 100) * 0.6;
  };

  return (
    <View style={styles.container}>
      <View style={styles.windDisplay}>
        {/* Wind Arrow */}
        <View style={[
          styles.arrow,
          {
            backgroundColor: getWindColor(windInfo.type, windInfo.isGust),
            transform: [
              { rotate: getArrowRotation(windInfo.direction) },
              { scale: getWindIntensityScale(windInfo.force) }
            ]
          }
        ]}>
          <View style={styles.arrowHead} />
        </View>
        
        {/* Wind Info */}
        <View style={styles.windInfo}>
          <Text style={[styles.windType, { color: getWindColor(windInfo.type, windInfo.isGust) }]}>
            {windInfo.isGust ? 'GUST!' : getWindStrength(windInfo.force)}
          </Text>
          <Text style={styles.windForce}>
            {windInfo.force.toFixed(0)} px/s
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 120,
    right: 20,
    zIndex: 1000,
  },
  windDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  arrow: {
    width: 24,
    height: 4,
    borderRadius: 2,
    marginRight: 8,
    position: 'relative',
  },
  arrowHead: {
    position: 'absolute',
    right: -2,
    top: -4,
    width: 0,
    height: 0,
    borderLeftWidth: 8,
    borderLeftColor: 'inherit',
    borderTopWidth: 6,
    borderTopColor: 'transparent',
    borderBottomWidth: 6,
    borderBottomColor: 'transparent',
  },
  windInfo: {
    alignItems: 'flex-start',
  },
  windType: {
    fontSize: 12,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  windForce: {
    fontSize: 10,
    color: '#666',
    marginTop: 1,
  },
});
