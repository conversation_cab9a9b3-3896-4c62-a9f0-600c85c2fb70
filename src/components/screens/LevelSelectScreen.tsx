import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Svg, { Path } from 'react-native-svg';
import { i18n } from '../../utils/i18n';
import { generateLevelMapLayout } from '../../types/GameTypes';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface LevelSelectScreenProps {
  currentWorld: number;
  unlockedLevels: number[];
  completedLevels: number[];
  onLevelSelect: (level: number) => void;
  onBackPress: () => void;
}

const LEVELS_PER_WORLD = 10;
const TOTAL_WORLDS = 5;

export const LevelSelectScreen: React.FC<LevelSelectScreenProps> = ({
  currentWorld,
  unlockedLevels,
  completedLevels,
  onLevelSelect,
  onBackPress,
}) => {
  const [translations, setTranslations] = useState(i18n.getTranslations());

  // Generate level map layout (memoized to prevent regeneration on re-renders)
  // Use compact height (75% of screen) to ensure all levels fit
  const levelMapLayout = useMemo(() => {
    return generateLevelMapLayout(SCREEN_WIDTH, SCREEN_HEIGHT * 0.75, LEVELS_PER_WORLD);
  }, []);

  useEffect(() => {
    const handleLanguageChange = () => {
      setTranslations(i18n.getTranslations());
    };

    i18n.addLanguageChangeListener(handleLanguageChange);

    return () => {
      i18n.removeLanguageChangeListener(handleLanguageChange);
    };
  }, []);
  // Render path connections between levels
  const renderPathConnections = () => {
    return levelMapLayout.connections.map((connection, index) => {
      const isUnlocked = unlockedLevels.includes(connection.to);
      const pathColor = isUnlocked ? '#4CAF50' : '#BDBDBD';
      const pathWidth = isUnlocked ? 4 : 2;

      // Create SVG path string for smooth curve
      const pathString = `M ${connection.pathPoints[0].x} ${connection.pathPoints[0].y} Q ${connection.pathPoints[1].x} ${connection.pathPoints[1].y} ${connection.pathPoints[2].x} ${connection.pathPoints[2].y}`;

      return (
        <Path
          key={`connection-${index}`}
          d={pathString}
          stroke={pathColor}
          strokeWidth={pathWidth}
          fill="none"
          strokeLinecap="round"
        />
      );
    });
  };

  // Render individual level node on the map
  const renderLevelNode = (level: number) => {
    const isUnlocked = unlockedLevels.includes(level);
    const isCompleted = completedLevels.includes(level);
    const isCurrentLevel = level === Math.max(...unlockedLevels);
    const position = levelMapLayout.positions[level];

    if (!position) return null;

    // Convert percentage position to actual pixels (using compact height)
    const x = (position.x / 100) * SCREEN_WIDTH;
    const y = (position.y / 100) * (SCREEN_HEIGHT * 0.75);

    return (
      <View
        key={level}
        style={[
          styles.levelContainer,
          {
            left: x - 25, // Center the node (50px width / 2)
            top: y - 25,  // Center the node (50px height / 2)
          }
        ]}
      >
        {/* Level circle */}
        <TouchableOpacity
          style={[
            styles.levelCircle,
            isCompleted && styles.completedLevel,
            isCurrentLevel && styles.currentLevel,
            !isUnlocked && styles.lockedLevel,
          ]}
          onPress={() => isUnlocked && onLevelSelect(level)}
          disabled={!isUnlocked}
        >
          <LinearGradient
            colors={
              isCompleted
                ? ['#FFD700', '#FFA500']
                : isCurrentLevel
                ? ['#4CAF50', '#45A049']
                : isUnlocked
                ? ['#2196F3', '#1976D2']
                : ['#9E9E9E', '#757575']
            }
            style={styles.levelGradient}
          >
            <Text style={[
              styles.levelText,
              !isUnlocked && styles.lockedText
            ]}>
              {isUnlocked ? level : '🔒'}
            </Text>
          </LinearGradient>
        </TouchableOpacity>

        {/* Stars for completed levels */}
        {isCompleted && (
          <View style={styles.starsContainer}>
            <Text style={styles.star}>⭐</Text>
            <Text style={styles.star}>⭐</Text>
            <Text style={styles.star}>⭐</Text>
          </View>
        )}
      </View>
    );
  };



  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#87CEEB', '#98D8E8', '#B0E0E6']}
        style={styles.background}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={onBackPress}>
            <Text style={styles.backButtonText}>{translations.back}</Text>
          </TouchableOpacity>
          <Text style={styles.worldTitle}>{translations.world} {currentWorld}</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Level path */}
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.pathContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.levelMap}>
            {/* SVG for path connections */}
            <Svg
              style={styles.pathSvg}
              width={SCREEN_WIDTH}
              height={SCREEN_HEIGHT * 0.75}
            >
              {renderPathConnections()}
            </Svg>

            {/* Level nodes */}
            {Array.from({ length: LEVELS_PER_WORLD }, (_, index) => {
              const level = index + 1;
              return renderLevelNode(level);
            })}
          </View>
        </ScrollView>

        {/* World progress */}
        <View style={styles.progressContainer}>
          <Text style={styles.progressText}>
            {translations.progress}: {completedLevels.length}/{LEVELS_PER_WORLD} {translations.levelsCompleted}
          </Text>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                { width: `${(completedLevels.length / LEVELS_PER_WORLD) * 100}%` }
              ]}
            />
          </View>
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 50,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    padding: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
  },
  backButtonText: {
    color: '#2E86AB',
    fontSize: 16,
    fontWeight: 'bold',
  },
  worldTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2E86AB',
    textAlign: 'center',
  },
  placeholder: {
    width: 80,
  },
  scrollView: {
    flex: 1,
  },
  pathContainer: {
    height: SCREEN_HEIGHT * 0.75, // More compact - use 75% of screen height
    width: SCREEN_WIDTH,
  },
  levelMap: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT * 0.75, // Match container height
    position: 'relative',
  },
  pathSvg: {
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 1,
  },
  levelContainer: {
    position: 'absolute',
    alignItems: 'center',
    width: 50,
    height: 50,
    zIndex: 2,
  },
  levelCircle: {
    width: 50,
    height: 50,
    borderRadius: 25,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    zIndex: 2,
  },
  levelGradient: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  completedLevel: {
    borderWidth: 3,
    borderColor: '#FFD700',
  },
  currentLevel: {
    borderWidth: 3,
    borderColor: '#4CAF50',
  },
  lockedLevel: {
    opacity: 0.6,
  },
  levelText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  lockedText: {
    fontSize: 14,
  },
  starsContainer: {
    flexDirection: 'row',
    marginTop: 5,
  },
  star: {
    fontSize: 12,
    marginHorizontal: 1,
  },
  progressContainer: {
    padding: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  progressText: {
    fontSize: 16,
    color: '#2E86AB',
    textAlign: 'center',
    marginBottom: 10,
    fontWeight: 'bold',
  },
  progressBar: {
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 4,
  },
});
