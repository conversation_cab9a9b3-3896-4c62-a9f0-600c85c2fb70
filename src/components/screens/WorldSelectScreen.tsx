import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Svg, { Path } from 'react-native-svg';
import { i18n } from '../../utils/i18n';
import { generateWorldMapLayout, WorldMapLayout } from '../../types/GameTypes';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface WorldSelectScreenProps {
  unlockedWorlds: number[];
  completedWorlds: number[];
  onWorldSelect: (world: number) => void;
  onBackPress: () => void;
}

const TOTAL_WORLDS = 5;
const WORLD_THEMES = [
  { nameKey: 'skyGarden', colors: ['#87CEEB', '#98D8E8'], emoji: '🌤️' },
  { nameKey: 'oceanDeep', colors: ['#006994', '#0080B7'], emoji: '🌊' },
  { nameKey: 'forestMagic', colors: ['#228B22', '#32CD32'], emoji: '🌲' },
  { nameKey: 'desertStorm', colors: ['#DAA520', '#F4A460'], emoji: '🏜️' },
  { nameKey: 'spaceOdyssey', colors: ['#191970', '#4B0082'], emoji: '🚀' },
];

export const WorldSelectScreen: React.FC<WorldSelectScreenProps> = ({
  unlockedWorlds,
  completedWorlds,
  onWorldSelect,
  onBackPress,
}) => {
  const [translations, setTranslations] = useState(i18n.getTranslations());

  // Generate world map layout (memoized to prevent regeneration on re-renders)
  const worldMapLayout = useMemo(() => {
    return generateWorldMapLayout(SCREEN_WIDTH, SCREEN_HEIGHT, TOTAL_WORLDS);
  }, []);

  useEffect(() => {
    const handleLanguageChange = () => {
      setTranslations(i18n.getTranslations());
    };

    i18n.addLanguageChangeListener(handleLanguageChange);

    return () => {
      i18n.removeLanguageChangeListener(handleLanguageChange);
    };
  }, []);
  // Render path connections between worlds
  const renderPathConnections = () => {
    return worldMapLayout.connections.map((connection, index) => {
      const isUnlocked = unlockedWorlds.includes(connection.to);
      const pathColor = isUnlocked ? '#4CAF50' : '#BDBDBD';
      const pathWidth = isUnlocked ? 4 : 2;

      // Create SVG path string for smooth curve
      const pathString = `M ${connection.pathPoints[0].x} ${connection.pathPoints[0].y} Q ${connection.pathPoints[1].x} ${connection.pathPoints[1].y} ${connection.pathPoints[2].x} ${connection.pathPoints[2].y}`;

      return (
        <Path
          key={`connection-${index}`}
          d={pathString}
          stroke={pathColor}
          strokeWidth={pathWidth}
          fill="none"
          strokeLinecap="round"
        />
      );
    });
  };

  // Render individual world node on the map
  const renderWorldNode = (worldNumber: number) => {
    const isUnlocked = unlockedWorlds.includes(worldNumber);
    const isCompleted = completedWorlds.includes(worldNumber);
    const theme = WORLD_THEMES[worldNumber - 1];
    const position = worldMapLayout.positions[worldNumber];

    if (!position) return null;

    // Convert percentage position to actual pixels
    const x = (position.x / 100) * SCREEN_WIDTH;
    const y = (position.y / 100) * SCREEN_HEIGHT;

    return (
      <View
        key={worldNumber}
        style={[
          styles.worldNode,
          {
            left: x - 40, // Center the node (80px width / 2)
            top: y - 40,  // Center the node (80px height / 2)
          }
        ]}
      >
        <TouchableOpacity
          style={[
            styles.worldCircle,
            !isUnlocked && styles.lockedNode,
          ]}
          onPress={() => isUnlocked && onWorldSelect(worldNumber)}
          disabled={!isUnlocked}
        >
          <LinearGradient
            colors={isUnlocked ? theme.colors : ['#9E9E9E', '#757575']}
            style={styles.worldCircleGradient}
          >
            <Text style={styles.worldEmoji}>
              {isUnlocked ? theme.emoji : '🔒'}
            </Text>
            <Text style={[
              styles.worldNodeNumber,
              !isUnlocked && styles.lockedText
            ]}>
              {worldNumber}
            </Text>
          </LinearGradient>
        </TouchableOpacity>

        {/* Stars for completed worlds */}
        {isCompleted && (
          <View style={styles.nodeStarsContainer}>
            <Text style={styles.nodeStar}>⭐</Text>
            <Text style={styles.nodeStar}>⭐</Text>
            <Text style={styles.nodeStar}>⭐</Text>
          </View>
        )}

        {/* World name label */}
        <View style={styles.worldLabel}>
          <Text style={[
            styles.worldLabelText,
            !isUnlocked && styles.lockedText
          ]}>
            {isUnlocked ? translations.worldNames[theme.nameKey as keyof typeof translations.worldNames] : translations.locked}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#1a1a2e', '#16213e', '#0f3460']}
        style={styles.background}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={onBackPress}>
            <Text style={styles.backButtonText}>{translations.back}</Text>
          </TouchableOpacity>
          <Text style={styles.title}>{translations.selectWorldTitle}</Text>
          <View style={styles.placeholder} />
        </View>

        {/* World Map */}
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.mapContainer}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          minimumZoomScale={0.8}
          maximumZoomScale={2.0}
          bouncesZoom={true}
        >
          <View style={styles.worldMap}>
            {/* SVG for path connections */}
            <Svg
              style={styles.pathSvg}
              width={SCREEN_WIDTH}
              height={SCREEN_HEIGHT}
            >
              {renderPathConnections()}
            </Svg>

            {/* World nodes */}
            {Array.from({ length: TOTAL_WORLDS }, (_, index) => {
              const worldNumber = index + 1;
              return renderWorldNode(worldNumber);
            })}
          </View>
        </ScrollView>

        {/* Overall progress */}
        <View style={styles.overallProgress}>
          <Text style={styles.overallProgressText}>
            {translations.overallProgress}: {completedWorlds.length}/{TOTAL_WORLDS} {translations.worldsCompleted}
          </Text>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                { width: `${(completedWorlds.length / TOTAL_WORLDS) * 100}%` }
              ]}
            />
          </View>
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 50,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    padding: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  placeholder: {
    width: 80,
  },
  scrollView: {
    flex: 1,
  },
  mapContainer: {
    minHeight: SCREEN_HEIGHT * 0.8,
    minWidth: SCREEN_WIDTH,
  },
  worldMap: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT * 0.8,
    position: 'relative',
  },
  pathSvg: {
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 1,
  },
  worldNode: {
    position: 'absolute',
    width: 80,
    height: 80,
    alignItems: 'center',
    zIndex: 2,
  },
  worldCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
  },
  worldCircleGradient: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  lockedNode: {
    opacity: 0.7,
  },
  worldEmoji: {
    fontSize: 20,
    marginBottom: 2,
  },
  worldNodeNumber: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  lockedText: {
    color: '#CCCCCC',
  },
  nodeStarsContainer: {
    flexDirection: 'row',
    marginTop: 2,
    justifyContent: 'center',
  },
  nodeStar: {
    fontSize: 10,
    marginHorizontal: 1,
  },
  worldLabel: {
    marginTop: 5,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    maxWidth: 120,
  },
  worldLabelText: {
    fontSize: 10,
    color: '#FFFFFF',
    textAlign: 'center',
    fontWeight: '600',
  },
  overallProgress: {
    padding: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  overallProgressText: {
    fontSize: 16,
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 10,
    fontWeight: 'bold',
  },
  progressBar: {
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 4,
  },
});
