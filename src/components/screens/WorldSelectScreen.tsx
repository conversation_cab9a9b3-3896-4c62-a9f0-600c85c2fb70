import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { i18n } from '../../utils/i18n';
import { TYPOGRAPHY, GAME_COLORS } from '../../utils/Typography';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface WorldSelectScreenProps {
  unlockedWorlds: number[];
  completedWorlds: number[];
  onWorldSelect: (world: number) => void;
  onBackPress: () => void;
}

const TOTAL_WORLDS = 5;
const WORLD_THEMES = [
  { nameKey: 'skyGarden', colors: ['#87CEEB', '#98D8E8'], emoji: '🌤️' },
  { nameKey: 'oceanDeep', colors: ['#006994', '#0080B7'], emoji: '🌊' },
  { nameKey: 'forestMagic', colors: ['#228B22', '#32CD32'], emoji: '🌲' },
  { nameKey: 'desertStorm', colors: ['#DAA520', '#F4A460'], emoji: '🏜️' },
  { nameKey: 'spaceOdyssey', colors: ['#191970', '#4B0082'], emoji: '🚀' },
];

export const WorldSelectScreen: React.FC<WorldSelectScreenProps> = ({
  unlockedWorlds,
  completedWorlds,
  onWorldSelect,
  onBackPress,
}) => {
  const [translations, setTranslations] = useState(i18n.getTranslations());

  useEffect(() => {
    const handleLanguageChange = () => {
      setTranslations(i18n.getTranslations());
    };

    i18n.addLanguageChangeListener(handleLanguageChange);

    return () => {
      i18n.removeLanguageChangeListener(handleLanguageChange);
    };
  }, []);
  const renderWorldCard = (worldNumber: number) => {
    const isUnlocked = unlockedWorlds.includes(worldNumber);
    const isCompleted = completedWorlds.includes(worldNumber);
    const theme = WORLD_THEMES[worldNumber - 1];

    return (
      <TouchableOpacity
        key={worldNumber}
        style={[
          styles.worldCard,
          !isUnlocked && styles.lockedCard,
        ]}
        onPress={() => isUnlocked && onWorldSelect(worldNumber)}
        disabled={!isUnlocked}
      >
        <LinearGradient
          colors={isUnlocked ? theme.colors : ['#9E9E9E', '#757575']}
          style={styles.worldGradient}
        >
          {/* World number and emoji */}
          <View style={styles.worldHeader}>
            <Text style={styles.worldEmoji}>
              {isUnlocked ? theme.emoji : '🔒'}
            </Text>
            <Text style={styles.worldNumber}>
              {translations.world} {worldNumber}
            </Text>
          </View>

          {/* World name */}
          <Text style={[
            styles.worldName,
            !isUnlocked && styles.lockedText
          ]}>
            {isUnlocked ? translations.worldNames[theme.nameKey as keyof typeof translations.worldNames] : translations.locked}
          </Text>

          {/* Completion status */}
          {isUnlocked && (
            <View style={styles.statusContainer}>
              {isCompleted ? (
                <View style={styles.completedBadge}>
                  <Text style={styles.completedText}>{translations.completed}</Text>
                </View>
              ) : (
                <Text style={styles.progressText}>{translations.inProgress}</Text>
              )}
            </View>
          )}

          {/* Stars for completed worlds */}
          {isCompleted && (
            <View style={styles.starsContainer}>
              <Text style={styles.bigStar}>⭐</Text>
              <Text style={styles.bigStar}>⭐</Text>
              <Text style={styles.bigStar}>⭐</Text>
            </View>
          )}

          {/* Lock overlay */}
          {!isUnlocked && (
            <View style={styles.lockOverlay}>
              <Text style={styles.lockText}>{translations.completePreviousWorld}</Text>
            </View>
          )}
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#1a1a2e', '#16213e', '#0f3460']}
        style={styles.background}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={onBackPress}>
            <Text style={styles.backButtonText}>{translations.back}</Text>
          </TouchableOpacity>
          <Text style={styles.title}>{translations.selectWorldTitle}</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Worlds grid */}
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.worldsContainer}
          showsVerticalScrollIndicator={false}
        >
          {Array.from({ length: TOTAL_WORLDS }, (_, index) => {
            const worldNumber = index + 1;
            return renderWorldCard(worldNumber);
          })}
        </ScrollView>

        {/* Overall progress */}
        <View style={styles.overallProgress}>
          <Text style={styles.overallProgressText}>
            {translations.overallProgress}: {completedWorlds.length}/{TOTAL_WORLDS} {translations.worldsCompleted}
          </Text>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                { width: `${(completedWorlds.length / TOTAL_WORLDS) * 100}%` }
              ]}
            />
          </View>
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 50,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    padding: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
  },
  backButtonText: {
    ...TYPOGRAPHY.button,
    color: GAME_COLORS.text.primary,
  },
  title: {
    ...TYPOGRAPHY.title,
    color: GAME_COLORS.text.primary,
    textAlign: 'center',
  },
  placeholder: {
    width: 80,
  },
  scrollView: {
    flex: 1,
  },
  worldsContainer: {
    padding: 20,
    paddingBottom: 40,
  },
  worldCard: {
    marginBottom: 20,
    borderRadius: 15,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
  },
  lockedCard: {
    opacity: 0.7,
  },
  worldGradient: {
    padding: 20,
    borderRadius: 15,
    minHeight: 120,
    position: 'relative',
  },
  worldHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  worldEmoji: {
    fontSize: 24,
    marginRight: 10,
  },
  worldNumber: {
    ...TYPOGRAPHY.subheading,
    color: GAME_COLORS.text.primary,
  },
  worldName: {
    ...TYPOGRAPHY.body,
    color: GAME_COLORS.text.primary,
    marginBottom: 10,
  },
  lockedText: {
    color: GAME_COLORS.text.disabled,
  },
  statusContainer: {
    marginBottom: 10,
  },
  completedBadge: {
    backgroundColor: 'rgba(76, 175, 80, 0.8)',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  completedText: {
    ...TYPOGRAPHY.caption,
    color: GAME_COLORS.text.primary,
    fontSize: 12,
  },
  progressText: {
    ...TYPOGRAPHY.caption,
    color: GAME_COLORS.text.primary,
    fontStyle: 'italic',
  },
  starsContainer: {
    flexDirection: 'row',
    position: 'absolute',
    top: 15,
    right: 15,
  },
  bigStar: {
    fontSize: 16,
    marginLeft: 2,
  },
  lockOverlay: {
    position: 'absolute',
    bottom: 10,
    left: 20,
    right: 20,
  },
  lockText: {
    ...TYPOGRAPHY.caption,
    color: GAME_COLORS.text.disabled,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  overallProgress: {
    padding: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  overallProgressText: {
    ...TYPOGRAPHY.body,
    color: GAME_COLORS.text.primary,
    textAlign: 'center',
    marginBottom: 10,
  },
  progressBar: {
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 4,
  },
});
